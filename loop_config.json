{"nodes": [{"id": "CombineTextComponent", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "CombineTextComponent", "input_schema": {"predefined_fields": [{"field_name": "main_input", "data_type": {"type": "string", "description": "The main text or list to combine. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "num_additional_inputs", "data_type": {"type": "number", "description": "Set the number of additional text inputs to show (1-10)."}, "required": false}, {"field_name": "separator", "data_type": {"type": "string", "description": "The character or string to join the text with."}, "required": false}, {"field_name": "input_1", "data_type": {"type": "string", "description": "Text for input 1. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_2", "data_type": {"type": "string", "description": "Text for input 2. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_3", "data_type": {"type": "string", "description": "Text for input 3. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_4", "data_type": {"type": "string", "description": "Text for input 4. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_5", "data_type": {"type": "string", "description": "Text for input 5. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_6", "data_type": {"type": "string", "description": "Text for input 6. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_7", "data_type": {"type": "string", "description": "Text for input 7. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_8", "data_type": {"type": "string", "description": "Text for input 8. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_9", "data_type": {"type": "string", "description": "Text for input 9. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_10", "data_type": {"type": "string", "description": "Text for input 10. Can be connected from another node or entered directly."}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "result", "data_type": {"type": "string", "description": "", "format": "string"}}, {"field_name": "error", "data_type": {"type": "string", "description": "", "format": "string"}}]}}]}, {"id": "MessageToDataComponent", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "MessageToDataComponent", "input_schema": {"predefined_fields": [{"field_name": "input_message", "data_type": {"type": "object", "description": "The Message object to extract fields from. Can be connected from another node or entered directly."}, "required": true}, {"field_name": "fields_to_extract", "data_type": {"type": "array", "description": "List of field names to extract from the message. Leave empty to extract all fields. Can be connected from another node or entered directly."}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "output", "data_type": {"type": "string", "description": "", "format": "string"}}, {"field_name": "error", "data_type": {"type": "string", "description": "", "format": "string"}}]}}]}, {"id": "LoopNode", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "LoopNode", "input_schema": {"predefined_fields": [{"field_name": "source_type", "data_type": {"type": "string", "description": "Choose whether to iterate over a list of items or a number range."}, "required": false}, {"field_name": "iteration_list", "data_type": {"type": "string", "description": "The array of items to be processed by the loop. Can be connected from another node or entered directly as JSON array."}, "required": false}, {"field_name": "batch_size", "data_type": {"type": "string", "description": "Number of items to process together in each iteration. For example, batch size 2 will process items in pairs: [item1, item2], [item3, item4], etc."}, "required": false}, {"field_name": "start", "data_type": {"type": "string", "description": "Starting number for the range. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "end", "data_type": {"type": "string", "description": "Ending number for the range (inclusive). Can be connected from another node or entered directly."}, "required": false}, {"field_name": "step", "data_type": {"type": "string", "description": "Step size for the range (default: 1). Can be connected from another node or entered directly."}, "required": false}, {"field_name": "parallel_execution", "data_type": {"type": "boolean", "description": "Execute loop iterations in parallel for better performance."}, "required": false}, {"field_name": "max_concurrent", "data_type": {"type": "number", "description": "Maximum number of iterations to run concurrently (1-20)."}, "required": false}, {"field_name": "preserve_order", "data_type": {"type": "boolean", "description": "Maintain the original order of items in the results."}, "required": false}, {"field_name": "iteration_timeout", "data_type": {"type": "number", "description": "Maximum time to wait for each iteration to complete (1-3600 seconds)."}, "required": false}, {"field_name": "aggregation_type", "data_type": {"type": "string", "description": "How to aggregate results from all iterations."}, "required": false}, {"field_name": "include_metadata", "data_type": {"type": "boolean", "description": "Include metadata (timing, iteration index, etc.) in results."}, "required": false}, {"field_name": "on_iteration_error", "data_type": {"type": "string", "description": "How to handle errors in individual iterations."}, "required": false}, {"field_name": "include_errors", "data_type": {"type": "boolean", "description": "Include error information in the final results."}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "current_item", "data_type": {"type": "object", "description": "", "format": "string"}}, {"field_name": "final_results", "data_type": {"type": "array", "description": "", "format": "string"}}]}}]}], "transitions": [{"id": "transition-LoopNode-1750775661045", "sequence": 1, "transition_type": "initial", "execution_type": "loop", "node_info": {"node_id": "LoopNode", "tools_to_use": [{"tool_id": 1, "tool_name": "LoopNode", "tool_params": {"items": [{"field_name": "source_type", "data_type": "string", "field_value": "number_range"}, {"field_name": "iteration_list", "data_type": "string", "field_value": ""}, {"field_name": "batch_size", "data_type": "string", "field_value": "1"}, {"field_name": "start", "data_type": "string", "field_value": ""}, {"field_name": "end", "data_type": "string", "field_value": "10"}, {"field_name": "step", "data_type": "string", "field_value": "2"}, {"field_name": "parallel_execution", "data_type": "boolean", "field_value": true}, {"field_name": "max_concurrent", "data_type": "number", "field_value": 3}, {"field_name": "preserve_order", "data_type": "boolean", "field_value": true}, {"field_name": "iteration_timeout", "data_type": "number", "field_value": 60}, {"field_name": "aggregation_type", "data_type": "string", "field_value": "collect_all"}, {"field_name": "include_metadata", "data_type": "boolean", "field_value": true}, {"field_name": "on_iteration_error", "data_type": "string", "field_value": "continue"}, {"field_name": "include_errors", "data_type": "boolean", "field_value": true}]}}], "input_data": [], "output_data": [{"to_transition_id": "transition-CombineTextComponent-1750769520925", "target_node_id": "Combine Text", "data_type": "string", "output_handle_registry": {"handle_mappings": [{"handle_id": "current_item", "result_path": "current_item", "edge_id": "reactflow__edge-LoopNode-1750775661045current_item-CombineTextComponent-1750769520925main_input"}]}}, {"to_transition_id": "transition-MessageToDataComponent-1750769557490", "target_node_id": "Message To Data", "data_type": "string", "output_handle_registry": {"handle_mappings": [{"handle_id": "final_results", "result_path": "final_results", "edge_id": "reactflow__edge-LoopNode-1750775661045final_results-MessageToDataComponent-1750769557490input_message"}]}}]}, "result_resolution": {"node_type": "loop", "expected_result_structure": "direct", "handle_registry": {"input_handles": [{"handle_id": "iteration_list", "handle_name": "Iteration List", "data_type": "string", "required": false, "description": "The array of items to be processed by the loop. Can be connected from another node or entered directly as JSON array."}, {"handle_id": "batch_size", "handle_name": "<PERSON><PERSON> Si<PERSON>", "data_type": "string", "required": false, "description": "Number of items to process together in each iteration. For example, batch size 2 will process items in pairs: [item1, item2], [item3, item4], etc."}, {"handle_id": "start", "handle_name": "Start Number", "data_type": "string", "required": false, "description": "Starting number for the range. Can be connected from another node or entered directly."}, {"handle_id": "end", "handle_name": "End Number", "data_type": "string", "required": false, "description": "Ending number for the range (inclusive). Can be connected from another node or entered directly."}, {"handle_id": "step", "handle_name": "Step Size", "data_type": "string", "required": false, "description": "Step size for the range (default: 1). Can be connected from another node or entered directly."}], "output_handles": [{"handle_id": "current_item", "handle_name": "Current Order (Iteration Output)", "data_type": "object", "description": ""}, {"handle_id": "final_results", "handle_name": "All Results (Exit Output)", "data_type": "array", "description": ""}]}, "result_path_hints": {"current_item": "current_item", "final_results": "final_results"}, "dynamic_discovery": {"enabled": false, "fallback_patterns": ["result.current_item", "output_data.current_item", "response.current_item", "data.current_item", "result.final_results", "output_data.final_results", "response.final_results", "data.final_results", "{handle_id}", "result", "output_data", "response", "data", "result.{handle_id}", "output_data.{handle_id}", "result.result", "response.data", "content", "value"], "validation_rules": [{"rule_type": "type_check", "rule_config": {"allowed_types": ["string", "number", "object", "array", "boolean"], "reject_null": false, "reject_undefined": true}}, {"rule_type": "structure_check", "rule_config": {"min_depth": 0, "max_depth": 5, "allow_nested_objects": true, "allow_arrays": true}}, {"rule_type": "content_check", "rule_config": {"min_length": 0, "reject_empty_strings": false, "reject_empty_objects": false, "reject_empty_arrays": false}}]}, "extraction_metadata": {"supports_multiple_outputs": true, "supports_nested_results": false, "requires_dynamic_discovery": false, "primary_output_handle": "current_item"}}, "approval_required": false, "end": false, "loop_config": {"iteration_behavior": "independent", "iteration_source": {"source_type": "number_range", "range_config": {"start": "", "end": "10", "step": "2"}}, "exit_condition": {"condition_type": "all_items_processed"}, "iteration_settings": {"parallel_execution": true, "max_concurrent": 3, "preserve_order": true, "iteration_timeout": 60}, "result_aggregation": {"aggregation_type": "collect_all", "include_metadata": true}, "error_handling": {"on_iteration_error": "continue", "include_errors": true}}}, {"id": "transition-CombineTextComponent-1750769520925", "sequence": 2, "transition_type": "standard", "execution_type": "Components", "node_info": {"node_id": "CombineTextComponent", "tools_to_use": [{"tool_id": 1, "tool_name": "CombineTextComponent", "tool_params": {"items": [{"field_name": "main_input", "data_type": "string", "field_value": null}, {"field_name": "num_additional_inputs", "data_type": "number", "field_value": "1"}, {"field_name": "separator", "data_type": "string", "field_value": "\\n"}, {"field_name": "input_1", "data_type": "string", "field_value": "coming"}, {"field_name": "input_2", "data_type": "string", "field_value": ""}, {"field_name": "input_3", "data_type": "string", "field_value": ""}, {"field_name": "input_4", "data_type": "string", "field_value": ""}, {"field_name": "input_5", "data_type": "string", "field_value": ""}, {"field_name": "input_6", "data_type": "string", "field_value": ""}, {"field_name": "input_7", "data_type": "string", "field_value": ""}, {"field_name": "input_8", "data_type": "string", "field_value": ""}, {"field_name": "input_9", "data_type": "string", "field_value": ""}, {"field_name": "input_10", "data_type": "string", "field_value": ""}]}}], "input_data": [{"from_transition_id": "transition-LoopNode-1750775661045", "source_node_id": "For Each Loop", "data_type": "string", "handle_mappings": [{"source_transition_id": "transition-LoopNode-1750775661045", "source_handle_id": "current_item", "target_handle_id": "main_input", "edge_id": "reactflow__edge-LoopNode-1750775661045current_item-CombineTextComponent-1750769520925main_input"}]}], "output_data": []}, "result_resolution": {"node_type": "component", "expected_result_structure": "direct", "handle_registry": {"input_handles": [{"handle_id": "main_input", "handle_name": "Main Input", "data_type": "string", "required": false, "description": "The main text or list to combine. Can be connected from another node or entered directly."}, {"handle_id": "input_1", "handle_name": "Input 1", "data_type": "string", "required": false, "description": "Text for input 1. Can be connected from another node or entered directly."}, {"handle_id": "input_2", "handle_name": "Input 2", "data_type": "string", "required": false, "description": "Text for input 2. Can be connected from another node or entered directly."}, {"handle_id": "input_3", "handle_name": "Input 3", "data_type": "string", "required": false, "description": "Text for input 3. Can be connected from another node or entered directly."}, {"handle_id": "input_4", "handle_name": "Input 4", "data_type": "string", "required": false, "description": "Text for input 4. Can be connected from another node or entered directly."}, {"handle_id": "input_5", "handle_name": "Input 5", "data_type": "string", "required": false, "description": "Text for input 5. Can be connected from another node or entered directly."}, {"handle_id": "input_6", "handle_name": "Input 6", "data_type": "string", "required": false, "description": "Text for input 6. Can be connected from another node or entered directly."}, {"handle_id": "input_7", "handle_name": "Input 7", "data_type": "string", "required": false, "description": "Text for input 7. Can be connected from another node or entered directly."}, {"handle_id": "input_8", "handle_name": "Input 8", "data_type": "string", "required": false, "description": "Text for input 8. Can be connected from another node or entered directly."}, {"handle_id": "input_9", "handle_name": "Input 9", "data_type": "string", "required": false, "description": "Text for input 9. Can be connected from another node or entered directly."}, {"handle_id": "input_10", "handle_name": "Input 10", "data_type": "string", "required": false, "description": "Text for input 10. Can be connected from another node or entered directly."}], "output_handles": [{"handle_id": "result", "handle_name": "Combined Text", "data_type": "string", "description": ""}, {"handle_id": "error", "handle_name": "Error", "data_type": "string", "description": ""}]}, "result_path_hints": {"result": "result", "error": "error"}, "dynamic_discovery": {"enabled": false, "fallback_patterns": ["result.result", "output_data.result", "response.result", "data.result", "result.error", "output_data.error", "response.error", "data.error", "{handle_id}", "result", "output_data", "response", "data", "result.{handle_id}", "output_data.{handle_id}", "result.result", "response.data", "content", "value"], "validation_rules": [{"rule_type": "type_check", "rule_config": {"allowed_types": ["string", "number", "object", "array", "boolean"], "reject_null": false, "reject_undefined": true}}, {"rule_type": "structure_check", "rule_config": {"min_depth": 0, "max_depth": 5, "allow_nested_objects": true, "allow_arrays": true}}, {"rule_type": "content_check", "rule_config": {"min_length": 0, "reject_empty_strings": false, "reject_empty_objects": false, "reject_empty_arrays": false}}]}, "extraction_metadata": {"supports_multiple_outputs": true, "supports_nested_results": false, "requires_dynamic_discovery": false, "primary_output_handle": "result"}}, "approval_required": false, "end": true}, {"id": "transition-MessageToDataComponent-1750769557490", "sequence": 3, "transition_type": "standard", "execution_type": "Components", "node_info": {"node_id": "MessageToDataComponent", "tools_to_use": [{"tool_id": 1, "tool_name": "MessageToDataComponent", "tool_params": {"items": [{"field_name": "input_message", "data_type": "object", "field_value": null}, {"field_name": "fields_to_extract", "data_type": "array", "field_value": null}]}}], "input_data": [{"from_transition_id": "transition-LoopNode-1750775661045", "source_node_id": "For Each Loop", "data_type": "string", "handle_mappings": [{"source_transition_id": "transition-LoopNode-1750775661045", "source_handle_id": "final_results", "target_handle_id": "input_message", "edge_id": "reactflow__edge-LoopNode-1750775661045final_results-MessageToDataComponent-1750769557490input_message"}]}], "output_data": []}, "result_resolution": {"node_type": "component", "expected_result_structure": "direct", "handle_registry": {"input_handles": [{"handle_id": "input_message", "handle_name": "Input Message", "data_type": "object", "required": true, "description": "The Message object to extract fields from. Can be connected from another node or entered directly."}, {"handle_id": "fields_to_extract", "handle_name": "Fields to Extract", "data_type": "array", "required": false, "description": "List of field names to extract from the message. Leave empty to extract all fields. Can be connected from another node or entered directly."}], "output_handles": [{"handle_id": "output", "handle_name": "Extracted Data", "data_type": "string", "description": ""}, {"handle_id": "error", "handle_name": "Error", "data_type": "string", "description": ""}]}, "result_path_hints": {"output": "output", "error": "error"}, "dynamic_discovery": {"enabled": false, "fallback_patterns": ["result.output", "output_data.output", "response.output", "data.output", "result.error", "output_data.error", "response.error", "data.error", "{handle_id}", "result", "output_data", "response", "data", "result.{handle_id}", "output_data.{handle_id}", "result.result", "response.data", "content", "value"], "validation_rules": [{"rule_type": "type_check", "rule_config": {"allowed_types": ["string", "number", "object", "array", "boolean"], "reject_null": false, "reject_undefined": true}}, {"rule_type": "structure_check", "rule_config": {"min_depth": 0, "max_depth": 5, "allow_nested_objects": true, "allow_arrays": true}}, {"rule_type": "content_check", "rule_config": {"min_length": 0, "reject_empty_strings": false, "reject_empty_objects": false, "reject_empty_arrays": false}}]}, "extraction_metadata": {"supports_multiple_outputs": true, "supports_nested_results": false, "requires_dynamic_discovery": false, "primary_output_handle": "output"}}, "approval_required": false, "end": true}]}