{"name": "each loop testing", "description": "each_loop_testing", "workflow_data": {"nodes": [{"id": "start-node", "type": "WorkflowNode", "position": {"x": 100, "y": 100}, "data": {"label": "Start", "type": "component", "originalType": "StartNode", "definition": {"name": "StartNode", "display_name": "Start", "description": "The starting point for all workflows. Only nodes connected to this node will be executed.", "category": "Input/Output", "icon": "Play", "beta": false, "inputs": [], "outputs": [{"name": "flow", "display_name": "Flow", "output_type": "Any"}], "is_valid": true, "path": "components.io.start_node"}, "config": {"collected_parameters": {"LoopNode-1750775661045_start": {"node_id": "LoopNode-1750775661045", "node_name": "For Each Loop", "input_name": "start", "connected_to_start": true, "required": true, "input_type": "string", "options": null}, "LoopNode-1750775661045_end": {"node_id": "LoopNode-1750775661045", "node_name": "For Each Loop", "input_name": "end", "value": "10", "connected_to_start": true, "required": true, "input_type": "string", "options": null}}}}, "width": 208, "height": 122, "selected": false, "dragging": false}, {"id": "CombineTextComponent-1750769520925", "type": "WorkflowNode", "position": {"x": 960, "y": 0}, "data": {"label": "Combine Text", "type": "component", "originalType": "CombineTextComponent", "definition": {"name": "CombineTextComponent", "display_name": "Combine Text", "description": "Joins text inputs with a separator, supporting a variable number of inputs.", "category": "Processing", "icon": "Link", "type": "component", "beta": false, "requires_approval": false, "inputs": [{"name": "main_input", "display_name": "Main Input", "info": "The main text or list to combine. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "num_additional_inputs", "display_name": "Number of Additional Inputs", "info": "Set the number of additional text inputs to show (1-10).", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 2, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "separator", "display_name": "Separator", "info": "The character or string to join the text with.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "\\n", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_1", "display_name": "Input 1", "info": "Text for input 1. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "1", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_2", "display_name": "Input 2", "info": "Text for input 2. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_3", "display_name": "Input 3", "info": "Text for input 3. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_4", "display_name": "Input 4", "info": "Text for input 4. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_5", "display_name": "Input 5", "info": "Text for input 5. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_6", "display_name": "Input 6", "info": "Text for input 6. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_7", "display_name": "Input 7", "info": "Text for input 7. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_8", "display_name": "Input 8", "info": "Text for input 8. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_9", "display_name": "Input 9", "info": "Text for input 9. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_10", "display_name": "Input 10", "info": "Text for input 10. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "result", "display_name": "Combined Text", "output_type": "string", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.processing.combinetextcomponent", "interface_issues": []}, "config": {"main_input": "", "num_additional_inputs": "1", "separator": "\\n", "input_1": "coming", "input_2": "", "input_3": "", "input_4": "", "input_5": "", "input_6": "", "input_7": "", "input_8": "", "input_9": "", "input_10": ""}}, "width": 208, "height": 148, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "MessageToDataComponent-1750769557490", "type": "WorkflowNode", "position": {"x": 1040, "y": 220}, "data": {"label": "Message To Data", "type": "component", "originalType": "MessageToDataComponent", "definition": {"name": "MessageToDataComponent", "display_name": "Message To Data", "description": "Extracts fields from a Message object.", "category": "Processing", "icon": "Package", "type": "component", "beta": false, "requires_approval": false, "inputs": [{"name": "input_message", "display_name": "Input Message", "info": "The Message object to extract fields from. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["Message", "dict", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "fields_to_extract", "display_name": "Fields to Extract", "info": "List of field names to extract from the message. Leave empty to extract all fields. Can be connected from another node or entered directly.", "input_type": "list", "input_types": ["list", "Any"], "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": [], "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "output", "display_name": "Extracted Data", "output_type": "dict", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.processing.messagetodatacomponent", "interface_issues": []}, "config": {}}, "width": 208, "height": 148, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "LoopNode-1750775661045", "type": "WorkflowNode", "position": {"x": 560, "y": 80}, "data": {"label": "For Each Loop", "type": "component", "originalType": "LoopNode", "definition": {"name": "LoopNode", "display_name": "For Each Loop", "description": "Iterates over a list, with advanced controls for parallelism, aggregation, and error handling.", "category": "Logic", "icon": "Repeat", "type": "component", "beta": false, "requires_approval": false, "inputs": [{"name": "source_type", "display_name": "Iteration Source", "info": "Choose whether to iterate over a list of items or a number range.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": true, "advanced": false, "value": "iteration_list", "options": ["iteration_list", "number_range"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "iteration_list", "display_name": "Iteration List", "info": "The array of items to be processed by the loop. Can be connected from another node or entered directly as JSON array.", "input_type": "string", "input_types": ["array", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "source_type", "field_value": "iteration_list", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": [{"field_name": "source_type", "field_value": "iteration_list", "operator": "equals"}], "requirement_logic": "OR"}, {"name": "batch_size", "display_name": "<PERSON><PERSON> Si<PERSON>", "info": "Number of items to process together in each iteration. For example, batch size 2 will process items in pairs: [item1, item2], [item3, item4], etc.", "input_type": "string", "input_types": ["number", "integer", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "1", "options": null, "visibility_rules": [{"field_name": "source_type", "field_value": "iteration_list", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "start", "display_name": "Start Number", "info": "Starting number for the range. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["number", "integer", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "1", "options": null, "visibility_rules": [{"field_name": "source_type", "field_value": "number_range", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": [{"field_name": "source_type", "field_value": "number_range", "operator": "equals"}], "requirement_logic": "OR"}, {"name": "end", "display_name": "End Number", "info": "Ending number for the range (inclusive). Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["number", "integer", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "10", "options": null, "visibility_rules": [{"field_name": "source_type", "field_value": "number_range", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": [{"field_name": "source_type", "field_value": "number_range", "operator": "equals"}], "requirement_logic": "OR"}, {"name": "step", "display_name": "Step Size", "info": "Step size for the range (default: 1). Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["number", "integer", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "1", "options": null, "visibility_rules": [{"field_name": "source_type", "field_value": "number_range", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "parallel_execution", "display_name": "Parallel Execution", "info": "Execute loop iterations in parallel for better performance.", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": true, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "max_concurrent", "display_name": "Max Concurrent Iterations", "info": "Maximum number of iterations to run concurrently (1-20).", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 3, "options": null, "visibility_rules": [{"field_name": "parallel_execution", "field_value": "True", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "preserve_order", "display_name": "Preserve Order", "info": "Maintain the original order of items in the results.", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": true, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "iteration_timeout", "display_name": "Iteration Timeout (seconds)", "info": "Maximum time to wait for each iteration to complete (1-3600 seconds).", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": 60, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "aggregation_type", "display_name": "Result Aggregation", "info": "How to aggregate results from all iterations.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "collect_all", "options": ["collect_all", "collect_successful", "count_only", "latest_only", "first_success", "combine_text"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "include_metadata", "display_name": "Include Metadata", "info": "Include metadata (timing, iteration index, etc.) in results.", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": true, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "on_iteration_error", "display_name": "On Iteration Error", "info": "How to handle errors in individual iterations.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "continue", "options": ["continue", "retry_once", "retry_twice", "exit_loop"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "include_errors", "display_name": "Include Errors in Results", "info": "Include error information in the final results.", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": true, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "current_item", "display_name": "Current Order (Iteration Output)", "output_type": "object", "semantic_type": null, "method": null}, {"name": "final_results", "display_name": "All Results (Exit Output)", "output_type": "array", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.logic.loopnode", "interface_issues": []}, "config": {"source_type": "number_range", "iteration_list": "", "batch_size": "1", "start": "", "end": "10", "step": "2", "parallel_execution": true, "max_concurrent": 3, "preserve_order": true, "iteration_timeout": 60, "aggregation_type": "collect_all", "include_metadata": true, "on_iteration_error": "continue", "include_errors": true}}, "width": 208, "height": 184, "selected": false, "dragging": false, "style": {"opacity": 1}}], "edges": [{"id": "reactflow__edge-start-nodeflow-LoopNode-1750775661045iteration_list", "source": "start-node", "sourceHandle": "flow", "target": "LoopNode-1750775661045", "targetHandle": "iteration_list", "type": "default", "animated": true}, {"id": "reactflow__edge-LoopNode-1750775661045current_item-CombineTextComponent-1750769520925main_input", "source": "LoopNode-1750775661045", "sourceHandle": "current_item", "target": "CombineTextComponent-1750769520925", "targetHandle": "main_input", "type": "default", "animated": true}, {"id": "reactflow__edge-LoopNode-1750775661045final_results-MessageToDataComponent-1750769557490input_message", "source": "LoopNode-1750775661045", "sourceHandle": "final_results", "target": "MessageToDataComponent-1750769557490", "targetHandle": "input_message", "type": "default", "animated": true}, {"id": "reactflow__edge-start-nodeflow-LoopNode-1750775661045start", "source": "start-node", "sourceHandle": "flow", "target": "LoopNode-1750775661045", "targetHandle": "start", "type": "default", "animated": true}, {"id": "reactflow__edge-start-nodeflow-LoopNode-1750775661045end", "source": "start-node", "sourceHandle": "flow", "target": "LoopNode-1750775661045", "targetHandle": "end", "type": "default", "animated": true}]}, "start_node_data": [{"field": "start", "type": "string", "transition_id": "transition-LoopNode-1750775661045"}, {"field": "end", "type": "string", "transition_id": "transition-LoopNode-1750775661045"}]}