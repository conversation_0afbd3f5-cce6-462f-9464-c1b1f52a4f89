"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { Eye, EyeOff } from "lucide-react";

interface EnvKey {
  key: string;
  description: string;
  value: string;
}

interface AddEnvKeysDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  toolName: string;
  envKeys: Array<{ key: string; description: string }>;
  onSubmit: (envKeyValues: Array<{ key: string; value: string }>) => void;
}

export const AddEnvKeysDialog: React.FC<AddEnvKeysDialogProps> = ({
  open,
  onOpenChange,
  toolName,
  envKeys,
  onSubmit,
}) => {
  // Initialize state with env keys
  const [keys, setKeys] = useState<EnvKey[]>(envKeys.map((key) => ({ ...key, value: "" })));

  // State to track which inputs should show their values
  const [showValues, setShowValues] = useState<Record<string, boolean>>({});

  const handleValueChange = (index: number, value: string) => {
    const newKeys = [...keys];
    newKeys[index].value = value;
    setKeys(newKeys);
  };

  const toggleShowValue = (key: string) => {
    setShowValues((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  const handleSubmit = () => {
    // Check if all keys have values
    const emptyKeys = keys.filter((key) => !key.value.trim());
    if (emptyKeys.length > 0) {
      toast.error(`Please provide values for all environment keys`);
      return;
    }

    // Format the data for submission
    const envKeyValues = keys.map((key) => ({
      key: key.key,
      value: key.value,
    }));

    onSubmit(envKeyValues);
    toast.success(`Environment keys for '${toolName}' submitted.`);
    onOpenChange(false);
  };

  const handleClose = () => {
    setKeys(envKeys.map((key) => ({ ...key, value: "" })));
    setShowValues({});
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Add Environment Keys for: {toolName}</DialogTitle>
          <DialogDescription>
            Please provide values for the required environment keys to use this tool.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {keys.map((envKey, index) => (
            <div key={envKey.key} className="space-y-2">
              <div className="flex justify-between">
                <label className="text-sm font-medium">{envKey.key}</label>
                <span className="text-muted-foreground text-xs">{envKey.description}</span>
              </div>
              <div className="flex">
                <Input
                  type={showValues[envKey.key] ? "text" : "password"}
                  value={envKey.value}
                  onChange={(e) => handleValueChange(index, e.target.value)}
                  placeholder={`Enter ${envKey.key}`}
                  className="flex-grow"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  onClick={() => toggleShowValue(envKey.key)}
                  className="ml-2"
                >
                  {showValues[envKey.key] ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          ))}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button onClick={handleSubmit}>Save Keys</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
