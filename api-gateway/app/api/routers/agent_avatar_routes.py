from fastapi import APIRouter, Depends, HTTPException, Query
from app.services.agent_service import AgentServiceClient
from app.schemas.agent import (
    AgentAvatarCreate,
    AgentAvatarInDB,
    AgentAvatarResponse,
    ListAgentAvatarsResponse,
    PaginationMetadata,

)
from app.core.auth_guard import role_required
from app.services.user_service import UserServiceClient
from app.utils.parse_error import parse_error
from google.protobuf.json_format import MessageToDict

avatar_router = APIRouter(prefix="/agent-avatars", tags=["agent-avatars"])
user_service = UserServiceClient()
agent_service = AgentServiceClient()


@avatar_router.post("", response_model=AgentAvatarResponse)
async def create_agent_avatar(
    avatar_data: AgentAvatarCreate, current_user: dict = Depends(role_required(["admin"]))
):
    """
    Create a new agent avatar.
    
    This endpoint allows administrators to add new avatars to the system that can be used by agents.
    
    Args:
        avatar_data: The avatar data including the URL
        current_user: The current authenticated user (must be an admin)
        
    Returns:
        AgentAvatarResponse: The created avatar details
    """
    try:
        # Validate user and get user details
        validate_response = await user_service.validate_user(current_user["user_id"])
        if not validate_response["success"]:
            raise HTTPException(status_code=400, detail=validate_response["message"])

        user_details = validate_response["user"]

        # Call the agent service to create the avatar
        response = await agent_service.create_agent_avatar(
            url=avatar_data.url,
            owner_details=user_details,
        )
    

        if not response.success:
            raise HTTPException(status_code=400, detail=response.message)

        agent_avatar = MessageToDict(response.avatar, preserving_proto_field_name=True)
        
        return AgentAvatarResponse(
            success=response.success,
            message=response.message,
            avatar=AgentAvatarInDB(**agent_avatar),
        )
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@avatar_router.get("", response_model=ListAgentAvatarsResponse)
async def list_agent_avatars(
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1, le=100),
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    List all agent avatars.
    
    This endpoint allows users to list all available avatars that can be used for agents.
    
    Args:
        page: The page number for pagination
        limit: The number of items per page
        current_user: The current authenticated user
        
    Returns:
        ListAgentAvatarsResponse: A list of avatars with pagination details
    """
    try:
        # Call the agent service to list avatars
        response = await agent_service.list_agent_avatars(
            page=page,
            limit=limit,
        )

        if not response.success:
            raise HTTPException(status_code=400, detail=response.message)

        agents_avatar = []
        for agent in response.avatars:
            agent_avatar_dict = MessageToDict(agent, preserving_proto_field_name=True)
            agents_avatar.append(AgentAvatarInDB(**agent_avatar_dict))  # Convert to Pydantic model

        # Calculate pagination metadata
        total = response.total
        total_pages = response.total_pages
        current_page = page
        has_next_page = current_page < total_pages
        has_previous_page = current_page > 1

        metadata = PaginationMetadata(
            total=total,
            totalPages=total_pages,
            currentPage=current_page,
            pageSize=limit,
            hasNextPage=has_next_page,
            hasPreviousPage=has_previous_page,
        )

        return ListAgentAvatarsResponse(
            avatars=agents_avatar,
            metadata=metadata,
        )
        
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@avatar_router.get("/{avatar_id}", response_model=AgentAvatarResponse)
async def get_agent_avatar(
    avatar_id: str, current_user: dict = Depends(role_required(["user", "admin"]))
):
    """
    Get a specific agent avatar by ID.
    
    This endpoint allows users to retrieve details of a specific avatar.
    
    Args:
        avatar_id: The ID of the avatar to retrieve
        current_user: The current authenticated user
        
    Returns:
        AgentAvatarResponse: The avatar details
    """
    try:
        # Call the agent service to get the avatar
        response = await agent_service.get_agent_avatar(avatar_id=avatar_id)

        if not response.success:
            raise HTTPException(status_code=404, detail=response.message)
        
        agent_avatar = MessageToDict(response.avatar, preserving_proto_field_name=True)

        return AgentAvatarResponse(
            success=response.success,
            message=response.message,
            avatar=AgentAvatarInDB(**agent_avatar),
        )
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@avatar_router.delete("/{avatar_id}", response_model=AgentAvatarResponse)
async def delete_agent_avatar(
    avatar_id: str, current_user: dict = Depends(role_required(["admin"]))
):
    """
    Delete an agent avatar.
    
    This endpoint allows administrators to delete avatars from the system.
    
    Args:
        avatar_id: The ID of the avatar to delete
        current_user: The current authenticated user (must be an admin)
        
    Returns:
        AgentAvatarResponse: The result of the deletion operation
    """
    try:
        # Validate user and get user details
        validate_response = await user_service.validate_user(current_user["user_id"])
        if not validate_response["success"]:
            raise HTTPException(status_code=400, detail=validate_response["message"])

        user_details = validate_response["user"]

        # Call the agent service to delete the avatar
        response = await agent_service.delete_agent_avatar(
            avatar_id=avatar_id,
            owner_details=user_details,
        )

        if not response.success:
            raise HTTPException(status_code=400, detail=response.message)

        return AgentAvatarResponse(
            success=response.success,
            message=response.message,
            avatar=None,
        )
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])
