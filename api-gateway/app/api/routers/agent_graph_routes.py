from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from app.services.agent_graph_service import AgentGraphServiceClient
from app.schemas.agent import (
    PaginatedWorkflowResponse,
    PaginationMetadata,
    AgentInDB,
)
from google.protobuf.json_format import MessageToDict
from app.core.auth_guard import role_required
from app.utils.parse_error import parse_error

agent_graph_router = APIRouter(prefix="/agent-graph", tags=["agent-graph"])
agent_graph_service = AgentGraphServiceClient()


@agent_graph_router.get("/users/{user_id}/agents", response_model=PaginatedWorkflowResponse)
async def list_agents_by_user_id(
    user_id: str,
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(10, ge=1, le=100, description="Page size"),
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Get all agents that a user has access to.

    This endpoint retrieves a paginated list of agents that the specified user
    has access to. Users can only access their own agents unless they are admins.

    Args:
        user_id (str): The ID of the user whose agents to retrieve
        page (int): Page number for pagination (default: 1)
        page_size (int): Number of agents per page (default: 10, max: 100)
        current_user (dict): The authenticated user

    Returns:
        PaginatedWorkflowResponse: Paginated list of agents

    Raises:
        HTTPException 403: If user tries to access another user's agents without admin role
        HTTPException 404: If user not found or no agents found
        HTTPException 500: If an unexpected server error occurs
    """
    try:
        # Check if user is trying to access their own agents or if they're an admin
        if current_user.get("role") != "admin" and current_user.get("user_id") != user_id:
            raise HTTPException(
                status_code=403, 
                detail="You can only access your own agents unless you are an admin"
            )

        response = await agent_graph_service.list_agents_by_user_id(
            user_id=user_id, page=page, page_size=page_size
        )

        if not response.success:
            raise HTTPException(status_code=404, detail="No agents found for user")

        # Convert agents to Pydantic models
        agents = []
        for agent in response.agents:
            agent_dict = MessageToDict(agent, preserving_proto_field_name=True)
            
            # Normalize enum values to lowercase to match schema expectations
            if "visibility" in agent_dict:
                agent_dict["visibility"] = agent_dict["visibility"].lower()
            if "category" in agent_dict:
                agent_dict["category"] = agent_dict["category"].lower()
            if "status" in agent_dict:
                agent_dict["status"] = agent_dict["status"].lower()
                
            agents.append(AgentInDB(**agent_dict))

        # Calculate pagination metadata
        total = response.total
        total_pages = response.total_pages
        current_page = page
        has_next_page = current_page < total_pages
        has_previous_page = current_page > 1

        metadata = PaginationMetadata(
            total=total,
            totalPages=total_pages,
            currentPage=current_page,
            pageSize=page_size,
            hasNextPage=has_next_page,
            hasPreviousPage=has_previous_page,
        )

        return PaginatedWorkflowResponse(data=agents, metadata=metadata)

    except HTTPException:
        raise
    except Exception as e:
        print(f"[ERROR] Unexpected error in list_agents_by_user_id: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@agent_graph_router.get("/organisations/{organisation_id}/agents")
async def get_all_agents_from_organisation(
    organisation_id: str,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Get all agents from all departments of an organization.

    This endpoint retrieves all agents that belong to any department within
    the specified organization. Access is controlled based on user permissions.

    Args:
        organisation_id (str): The ID of the organization
        current_user (dict): The authenticated user

    Returns:
        dict: Response containing list of agents and metadata

    Raises:
        HTTPException 403: If user doesn't have access to the organization
        HTTPException 404: If organization not found or no agents found
        HTTPException 500: If an unexpected server error occurs
    """
    try:
        # TODO: Add organization access validation
        # For now, we'll allow any authenticated user to access
        
        response = await agent_graph_service.get_all_agents_from_organisation(
            organisation_id=organisation_id
        )

        if not response.success:
            raise HTTPException(
                status_code=404, 
                detail="No agents found for organization or organization not found"
            )

        # Convert agents to Pydantic models
        agents = []
        for agent in response.agents:
            agent_dict = MessageToDict(agent, preserving_proto_field_name=True)
            
            # Normalize enum values to lowercase
            if "visibility" in agent_dict:
                agent_dict["visibility"] = agent_dict["visibility"].lower()
            if "category" in agent_dict:
                agent_dict["category"] = agent_dict["category"].lower()
            if "status" in agent_dict:
                agent_dict["status"] = agent_dict["status"].lower()
                
            agents.append(AgentInDB(**agent_dict))

        return {
            "success": True,
            "message": f"Found {len(agents)} agents in organization",
            "agents": agents,
            "total": len(agents)
        }

    except HTTPException:
        raise
    except Exception as e:
        print(f"[ERROR] Unexpected error in get_all_agents_from_organisation: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@agent_graph_router.get("/departments/{department_id}/agents")
async def get_agents_from_department(
    department_id: str,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Get all agents from a specific department.

    This endpoint retrieves all agents that belong to the specified department.
    Access is controlled based on user permissions.

    Args:
        department_id (str): The ID of the department
        current_user (dict): The authenticated user

    Returns:
        dict: Response containing list of agents and metadata

    Raises:
        HTTPException 403: If user doesn't have access to the department
        HTTPException 404: If department not found or no agents found
        HTTPException 500: If an unexpected server error occurs
    """
    try:
        # TODO: Add department access validation
        # For now, we'll allow any authenticated user to access
        
        response = await agent_graph_service.get_agents_from_department(
            department_id=department_id
        )

        if not response.success:
            raise HTTPException(
                status_code=404, 
                detail="No agents found for department or department not found"
            )

        # Convert agents to Pydantic models
        agents = []
        for agent in response.agents:
            agent_dict = MessageToDict(agent, preserving_proto_field_name=True)
            
            # Normalize enum values to lowercase
            if "visibility" in agent_dict:
                agent_dict["visibility"] = agent_dict["visibility"].lower()
            if "category" in agent_dict:
                agent_dict["category"] = agent_dict["category"].lower()
            if "status" in agent_dict:
                agent_dict["status"] = agent_dict["status"].lower()
                
            agents.append(AgentInDB(**agent_dict))

        return {
            "success": True,
            "message": f"Found {len(agents)} agents in department",
            "agents": agents,
            "total": len(agents)
        }

    except HTTPException:
        raise
    except Exception as e:
        print(f"[ERROR] Unexpected error in get_agents_from_department: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@agent_graph_router.get("/users/{user_id}/agents/{agent_id}/access")
async def check_user_agent_access(
    user_id: str,
    agent_id: str,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Check if a user has access to a specific agent.

    This endpoint checks whether the specified user has access to the given agent.
    This is useful for authorization checks before allowing agent operations.

    Args:
        user_id (str): The ID of the user
        agent_id (str): The ID of the agent
        current_user (dict): The authenticated user

    Returns:
        dict: Response indicating whether user has access

    Raises:
        HTTPException 403: If current user cannot check access for the specified user
        HTTPException 500: If an unexpected server error occurs
    """
    try:
        # Check if user is checking their own access or if they're an admin
        if current_user.get("role") != "admin" and current_user.get("user_id") != user_id:
            raise HTTPException(
                status_code=403, 
                detail="You can only check access for your own agents unless you are an admin"
            )

        response = await agent_graph_service.check_user_agent_access(
            user_id=user_id, agent_id=agent_id
        )

        return {
            "success": response.success,
            "has_access": response.has_access,
            "message": "Access check completed"
        }

    except HTTPException:
        raise
    except Exception as e:
        print(f"[ERROR] Unexpected error in check_user_agent_access: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])