import json
from typing import Any, Dict, Optional

from ..helper.api_call import AuthType, HttpRequestHelper
from ..shared.config.base import get_settings


class AgentFetchService:
    def __init__(self):
        self.settings = get_settings()
        self.http_request_service = HttpRequestHelper(
            self.settings.gateway.api_url,
            auth_token=self.settings.gateway.api_key,
            auth_type=AuthType.API_KEY,
            api_key_name="X-Agent-Platform-Auth-Key",
        )

    async def fetch_agent_config(self, agent_id):
        """
        Fetches an agent configuration from the database.

        Args:
            agent_id (str): The ID of the agent to fetch.

        Returns:
            dict: The agent configuration.
        """

        response = self.http_request_service.get(
            endpoint=f"agents/agent-platform/{agent_id}"
        )
        return response.get("agent")

    async def fetch_mcps_by_ids(self, ids):
        """
        Fetches MCPs by their IDs using the agent-platform API.

        Args:
            ids (list): List of MCP IDs to fetch.

        Returns:
            dict: The API response containing the MCPs.
        """
        payload = {"ids": ids}

        response = self.http_request_service.post(
            endpoint="mcps/agent-platform/by-ids", data=payload
        )
        return response.get("mcps")

    async def fetch_agent_config_hardcoded(
        self,
        agent_id: str,
    ) -> Optional[Dict[str, Any]]:
        """
        Provides hardcoded agent configurations for testing by reading from a JSON file.
        """
        print(f"Fetching *HARDCODED* agent config for token/id: {agent_id}")

        config_file_path = "./config/agents.json"

        try:
            with open(config_file_path, "r") as file:
                agent_configs = json.load(file)
                print(f"Loaded agent configurations from {config_file_path}")
        except FileNotFoundError:
            print(f"[ERROR] Configuration file not found: {config_file_path}")
            return None
        except json.JSONDecodeError as e:
            print(f"[ERROR] Failed to parse JSON from {config_file_path}: {e}")
            return None

        agent_config = agent_configs.get(agent_id)

        if agent_config:
            print(f"Hardcoded agent config found for: {agent_id}")
            return agent_config
        else:
            print(f"No hardcoded agent config found for: {agent_id}")
            return None
