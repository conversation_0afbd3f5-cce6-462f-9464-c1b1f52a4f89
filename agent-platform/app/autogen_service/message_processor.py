from typing import Any, Dict, Optional, Tuple

from autogen_agentchat.base import Response, TaskResult
from autogen_agentchat.messages import (
    BaseAgentEvent,
    BaseChatMessage,
    HandoffMessage,
    ModelClientStreamingChunkEvent,
    MultiModalMessage,
    StopMessage,
    StructuredMessage,
    TextMessage,
    ToolCallExecutionEvent,
    ToolCallRequestEvent,
    ToolCallSummaryMessage,
    UserInputRequestedEvent,
)

from ..schemas.chat import AgentResponse, MessageType

# FunctionCall and FunctionExecutionResult are handled via hasattr checks


class MessageProcessor:
    """Handles message processing and extraction logic"""

    @staticmethod
    def get_message_type(message: Any) -> MessageType:
        """Determine message type from message instance with comprehensive coverage"""

        # Direct type mapping for known message classes
        type_mapping = {
            TextMessage: MessageType.TEXT,
            MultiModalMessage: MessageType.MULTIMODAL,
            ToolCallRequestEvent: MessageType.TOOL_CALL_REQUEST,
            ToolCallExecutionEvent: MessageType.TOOL_CALL_EXECUTION,
            ToolCallSummaryMessage: MessageType.TOOL_CALL_SUMMARY,
            TaskResult: MessageType.TASK_RESULT,
            ModelClientStreamingChunkEvent: MessageType.STREAMING_CHUNK,
            UserInputRequestedEvent: MessageType.USER_INPUT_REQUEST,
            Response: MessageType.RESPONSE,
            BaseAgentEvent: MessageType.AGENT_EVENT,
            HandoffMessage: MessageType.HANDOFF_MESSAGE,
            StopMessage: MessageType.TERMINATION_MESSAGE,
            StructuredMessage: MessageType.TEXT,  # Treat as text with structured content
        }

        # Check direct type mapping first
        for msg_class, msg_type in type_mapping.items():
            if isinstance(message, msg_class):
                return msg_type

        # Handle FunctionCall objects (from autogen_core.models)
        if (
            hasattr(message, "name")
            and hasattr(message, "arguments")
            and hasattr(message, "id")
        ):
            return MessageType.TOOL_CALL_REQUEST

        # Handle FunctionExecutionResult objects
        if (
            hasattr(message, "call_id")
            and hasattr(message, "name")
            and hasattr(message, "content")
            and hasattr(message, "is_error")
        ):
            return MessageType.TOOL_CALL_EXECUTION

        # Handle BaseChatMessage subclasses (fallback for other chat messages)
        if isinstance(message, BaseChatMessage):
            return MessageType.TEXT

        # Handle generic agent events
        if isinstance(message, BaseAgentEvent):
            return MessageType.AGENT_EVENT

        return MessageType.UNKNOWN

    @staticmethod
    def extract_message_content(message: Any) -> Tuple[str, str, Dict[str, Any]]:
        """Extract source and content from message with comprehensive event handling"""

        # Handle TextMessage
        if isinstance(message, TextMessage):
            source = getattr(message, "source", "assistant")
            content = getattr(message, "content", "")
            metadata = {}

            # Extract models usage if available
            if hasattr(message, "models_usage") and message.models_usage:
                metadata["models_usage"] = {
                    "prompt_tokens": getattr(message.models_usage, "prompt_tokens", 0),
                    "completion_tokens": getattr(
                        message.models_usage, "completion_tokens", 0
                    ),
                }

            return source, content, metadata

        # Handle StopMessage
        elif isinstance(message, StopMessage):
            source = getattr(message, "source", "assistant")
            content = getattr(message, "content", "")
            metadata = {"message_type": "stop", "stop_reason": "explicit_stop"}
            return source, str(content), metadata

        # Handle StructuredMessage
        elif isinstance(message, StructuredMessage):
            source = getattr(message, "source", "assistant")
            content_obj = getattr(message, "content", None)

            if content_obj:
                # Try to extract structured content
                if hasattr(content_obj, "thoughts") and hasattr(
                    content_obj, "response"
                ):
                    content = f"Thoughts: {content_obj.thoughts}\nResponse: {content_obj.response}"
                    metadata = {
                        "structured_content": {
                            "thoughts": content_obj.thoughts,
                            "response": content_obj.response,
                        }
                    }
                else:
                    content = str(content_obj)
                    metadata = {"structured_content": content_obj}
            else:
                content = "[Structured Message]"
                metadata = {"structured_content": None}

            return source, content, metadata

        # Handle MultiModalMessage
        elif isinstance(message, MultiModalMessage):
            source = getattr(message, "source", "assistant")
            content_parts = []
            attachment_details = []

            for content_item in message.content:
                if isinstance(content_item, str):
                    content_parts.append(content_item)
                else:
                    # For non-text content (images, etc.), add a placeholder
                    attachment_type = type(content_item).__name__
                    content_parts.append(f"[Attachment: {attachment_type}]")
                    attachment_details.append(
                        {
                            "type": attachment_type,
                            "data": (
                                str(content_item)
                                if hasattr(content_item, "__str__")
                                else attachment_type
                            ),
                        }
                    )

            content = (
                " ".join(content_parts) if content_parts else "[MultiModal Message]"
            )
            metadata = {
                "attachments": content_parts,
                "attachment_details": attachment_details,
                "content_count": len(message.content),
            }
            return source, content, metadata

        # Handle ToolCallRequestEvent
        elif isinstance(message, ToolCallRequestEvent):
            source = getattr(message, "source", "assistant")
            tool_calls = []
            tool_names = []

            if hasattr(message, "content") and isinstance(message.content, list):
                for call in message.content:
                    if hasattr(call, "name"):
                        tool_names.append(call.name)
                        tool_calls.append(
                            {
                                "id": getattr(call, "id", None),
                                "name": call.name,
                                "arguments": getattr(call, "arguments", ""),
                                "type": "function",
                            }
                        )
                content = f"Tool call request: {', '.join(tool_names)}"
            else:
                content = "Tool call request"

            metadata = {
                "tool_calls": tool_calls,
                "tool_names": tool_names,
                "call_count": len(tool_calls),
            }
            return source, content, metadata

        # Handle ToolCallExecutionEvent
        elif isinstance(message, ToolCallExecutionEvent):
            source = getattr(message, "source", "assistant")
            execution_results = []
            result_summaries = []

            if hasattr(message, "content") and isinstance(message.content, list):
                for result in message.content:
                    if hasattr(result, "name") and hasattr(result, "content"):
                        is_error = getattr(result, "is_error", False)
                        status = "ERROR" if is_error else "SUCCESS"
                        result_summaries.append(f"{result.name}: {status}")

                        execution_results.append(
                            {
                                "call_id": getattr(result, "call_id", None),
                                "name": result.name,
                                "content": result.content,
                                "is_error": is_error,
                                "status": status,
                            }
                        )
                content = f"Tool execution results: {', '.join(result_summaries)}"
            else:
                content = "Tool execution completed"

            metadata = {
                "execution_results": execution_results,
                "result_summaries": result_summaries,
                "execution_count": len(execution_results),
                "has_errors": any(r.get("is_error", False) for r in execution_results),
            }
            return source, content, metadata

        # Handle ToolCallSummaryMessage
        elif isinstance(message, ToolCallSummaryMessage):
            source = getattr(message, "source", "assistant")
            content = getattr(message, "content", "Tool call summary")
            metadata = {
                "summary": content,
                "message_type": "tool_call_summary",
            }
            return source, str(content), metadata

        # Handle Response objects
        elif isinstance(message, Response):
            if hasattr(message, "chat_message") and message.chat_message:
                chat_msg = message.chat_message
                source = getattr(chat_msg, "source", "assistant")
                content = getattr(chat_msg, "content", "")

                inner_messages = []
                inner_count = 0

                # Process inner messages if present
                if hasattr(message, "inner_messages") and message.inner_messages:
                    inner_count = len(message.inner_messages)
                    content += f" [+{inner_count} inner messages]"

                    for inner_msg in message.inner_messages:
                        inner_messages.append(
                            {
                                "content": getattr(inner_msg, "content", ""),
                                "source": getattr(inner_msg, "source", "assistant"),
                                "type": type(inner_msg).__name__,
                            }
                        )

                metadata = {
                    "chat_message": {
                        "content": getattr(chat_msg, "content", ""),
                        "source": getattr(chat_msg, "source", "assistant"),
                        "type": type(chat_msg).__name__,
                    },
                    "inner_messages": inner_messages,
                    "inner_message_count": inner_count,
                }
                return source, content, metadata
            else:
                return "assistant", "Response received", {"empty_response": True}

        # Handle TaskResult
        elif isinstance(message, TaskResult):
            source = "system"
            messages = getattr(message, "messages", [])
            message_count = len(messages)
            stop_reason = getattr(message, "stop_reason", "unknown")
            usage = getattr(message, "usage", None)

            content = f"Task completed: {message_count} messages, stopped due to {stop_reason}"

            # Extract message details
            message_details = []
            for msg in messages:
                message_details.append(
                    {
                        "content": getattr(msg, "content", ""),
                        "source": getattr(msg, "source", "assistant"),
                        "type": type(msg).__name__,
                    }
                )

            metadata = {
                "messages": message_details,
                "message_count": message_count,
                "stop_reason": stop_reason,
                "usage": usage,
                "task_completed": True,
            }
            return source, content, metadata

        # Handle ModelClientStreamingChunkEvent
        elif isinstance(message, ModelClientStreamingChunkEvent):
            source = getattr(message, "source", "assistant")
            content = getattr(message, "content", "")

            metadata = {
                "streaming": True,
                "chunk_type": "model_client_streaming",
            }

            # Extract chunk-specific information
            if hasattr(message, "chunk"):
                metadata["chunk_info"] = {
                    "chunk_type": type(message.chunk).__name__,
                    "chunk_data": str(message.chunk),
                }

            return source, str(content), metadata

        # Handle UserInputRequestedEvent
        elif isinstance(message, UserInputRequestedEvent):
            source = getattr(message, "source", "assistant")
            content = getattr(message, "content", "User input requested")

            metadata = {
                "user_input_requested": True,
                "event_type": "user_input_request",
                "prompt": content,
            }

            # Extract additional context if available
            if hasattr(message, "context"):
                metadata["context"] = getattr(message, "context", None)

            return source, str(content), metadata

        # Handle HandoffMessage
        elif isinstance(message, HandoffMessage):
            source = getattr(message, "source", "assistant")
            target = getattr(message, "target", "unknown")
            content = getattr(message, "content", "")
            handoff_content = f"Handoff to {target}: {content}"

            metadata = {
                "handoff": True,
                "source_agent": source,
                "target_agent": target,
                "handoff_content": content,
                "handoff_type": "agent_handoff",
            }

            return source, handoff_content, metadata

        # # Handle BaseAgentEvent (generic agent events)
        # elif isinstance(message, BaseAgentEvent):
        #     source = getattr(message, "source", "agent")
        #     content = getattr(
        #         message, "content", f"Agent event: {type(message).__name__}"
        #     )
        #     metadata = {
        #         "event_type": "agent_event",
        #         "event_class": type(message).__name__,
        #         "is_base_agent_event": True,
        #     }

        #     # Extract additional event attributes
        #     for attr in ["id", "timestamp", "context", "metadata"]:
        #         if hasattr(message, attr):
        #             metadata[f"event_{attr}"] = getattr(message, attr)

        #     return source, content, metadata

        # # Handle BaseChatMessage (fallback for other chat messages)
        # elif isinstance(message, BaseChatMessage):
        #     source = getattr(message, "source", "assistant")
        #     content = getattr(message, "content", "")
        #     metadata = {
        #         "message_type": "chat_message",
        #         "message_class": type(message).__name__,
        #         "is_base_chat_message": True,
        #     }

        #     # Extract models usage if available
        #     if hasattr(message, "models_usage") and message.models_usage:
        #         metadata["models_usage"] = {
        #             "prompt_tokens": getattr(message.models_usage, "prompt_tokens", 0),
        #             "completion_tokens": getattr(
        #                 message.models_usage, "completion_tokens", 0
        #             ),
        #         }

        #     return source, content, metadata

        # Handle FunctionCall objects
        elif hasattr(message, "name") and hasattr(message, "arguments"):
            # This is likely a FunctionCall object
            source = "assistant"
            content = f"Function call: {message.name}({message.arguments})"
            metadata = {
                "function_call": True,
                "function_name": message.name,
                "function_arguments": getattr(message, "arguments", ""),
                "function_id": getattr(message, "id", None),
                "call_type": "function",
            }
            return source, content, metadata

        # Handle FunctionExecutionResult objects
        elif (
            hasattr(message, "call_id")
            and hasattr(message, "name")
            and hasattr(message, "content")
        ):
            # This is likely a FunctionExecutionResult object
            source = "system"
            is_error = getattr(message, "is_error", False)
            status = "ERROR" if is_error else "SUCCESS"
            content = f"Function result [{status}] {message.name}: {message.content}"
            metadata = {
                "function_execution_result": True,
                "call_id": getattr(message, "call_id", None),
                "function_name": message.name,
                "result_content": message.content,
                "is_error": is_error,
                "status": status,
            }
            return source, content, metadata

        return "unknown", "", {}

    @staticmethod
    def extract_models_usage(message: Any) -> Dict[str, int]:
        """Extract token usage from message"""
        if hasattr(message, "models_usage") and message.models_usage:
            return {
                "prompt_tokens": getattr(message.models_usage, "prompt_tokens", 0),
                "completion_tokens": getattr(
                    message.models_usage, "completion_tokens", 0
                ),
            }
        return {"prompt_tokens": 0, "completion_tokens": 0}

    @staticmethod
    def extract_tool_call_details(message: Any) -> Optional[Dict[str, Any]]:
        """Extract tool call details from ToolCallRequestEvent"""
        if isinstance(message, ToolCallRequestEvent):
            tool_calls = []
            if hasattr(message, "content") and isinstance(message.content, list):
                for call in message.content:
                    if hasattr(call, "name") and hasattr(call, "arguments"):
                        tool_calls.append(
                            {
                                "id": getattr(call, "id", None),
                                "name": call.name,
                                "arguments": call.arguments,
                                "type": "function",
                            }
                        )
            return {
                "tool_calls": tool_calls,
                "source": getattr(message, "source", "assistant"),
            }
        return None

    @staticmethod
    def extract_tool_execution_details(message: Any) -> Optional[Dict[str, Any]]:
        """Extract tool execution details from ToolCallExecutionEvent"""
        if isinstance(message, ToolCallExecutionEvent):
            executions = []
            if hasattr(message, "content") and isinstance(message.content, list):
                for result in message.content:
                    execution_detail = {
                        "call_id": getattr(result, "call_id", None),
                        "name": getattr(result, "name", None),
                        "content": getattr(result, "content", None),
                        "is_error": getattr(result, "is_error", False),
                    }
                    executions.append(execution_detail)
            return {
                "tool_executions": executions,
                "source": getattr(message, "source", "assistant"),
            }
        return None

    @staticmethod
    def extract_response_details(message: Any) -> Optional[Dict[str, Any]]:
        """Extract details from Response object"""
        if isinstance(message, Response):
            details = {
                "chat_message": None,
                "inner_messages": [],
                "source": "assistant",
            }

            if hasattr(message, "chat_message") and message.chat_message:
                chat_msg = message.chat_message
                details["chat_message"] = {
                    "content": getattr(chat_msg, "content", ""),
                    "source": getattr(chat_msg, "source", "assistant"),
                    "type": type(chat_msg).__name__,
                }
                details["source"] = getattr(chat_msg, "source", "assistant")

            if hasattr(message, "inner_messages") and message.inner_messages:
                for inner_msg in message.inner_messages:
                    details["inner_messages"].append(
                        {
                            "content": getattr(inner_msg, "content", ""),
                            "source": getattr(inner_msg, "source", "assistant"),
                            "type": type(inner_msg).__name__,
                        }
                    )

            return details
        return None

    @staticmethod
    def extract_task_result_details(message: Any) -> Optional[Dict[str, Any]]:
        """Extract details from TaskResult"""
        if isinstance(message, TaskResult):
            return {
                "messages": [
                    {
                        "content": getattr(msg, "content", ""),
                        "source": getattr(msg, "source", "assistant"),
                        "type": type(msg).__name__,
                    }
                    for msg in getattr(message, "messages", [])
                ],
                "stop_reason": getattr(message, "stop_reason", None),
                "usage": getattr(message, "usage", None),
            }
        return None

    @staticmethod
    def extract_handoff_details(message: Any) -> Optional[Dict[str, Any]]:
        """Extract details from HandoffMessage"""
        if isinstance(message, HandoffMessage):
            return {
                "source": getattr(message, "source", "assistant"),
                "target": getattr(message, "target", "unknown"),
                "content": getattr(message, "content", ""),
                "handoff_type": "agent_handoff",
            }
        return None

    @staticmethod
    def extract_group_chat_details(message: Any) -> Optional[Dict[str, Any]]:
        """Extract group chat specific details from messages"""
        details = {}

        # Check for speaker selection information
        if hasattr(message, "source"):
            details["current_speaker"] = message.source

        # Check for team/group context
        if hasattr(message, "team_id"):
            details["team_id"] = message.team_id
        elif hasattr(message, "group_id"):
            details["group_id"] = message.group_id

        # Check for round-robin or selector information
        if hasattr(message, "next_speaker"):
            details["next_speaker"] = message.next_speaker

        # Check for termination conditions
        if hasattr(message, "termination_reason"):
            details["termination_reason"] = message.termination_reason

        # Check for participant information
        if hasattr(message, "participants"):
            details["participants"] = message.participants

        return details if details else None

    @staticmethod
    def extract_selector_group_chat_details(
        message: Any,
    ) -> Optional[Dict[str, Any]]:
        """Extract SelectorGroupChat specific details from messages"""
        details = {}

        # Check for selector-specific attributes
        if hasattr(message, "selector_result"):
            details["selector_result"] = message.selector_result

        # Check for speaker selection reasoning
        if hasattr(message, "selection_reason"):
            details["selection_reason"] = message.selection_reason

        # Check for available speakers
        if hasattr(message, "available_speakers"):
            details["available_speakers"] = message.available_speakers

        # Check for selector function information
        if hasattr(message, "selector_func_name"):
            details["selector_func_name"] = message.selector_func_name

        # Check for repeated speaker allowance
        if hasattr(message, "allow_repeated_speaker"):
            details["allow_repeated_speaker"] = message.allow_repeated_speaker

        # Check for speaker selection history
        if hasattr(message, "speaker_history"):
            details["speaker_history"] = message.speaker_history

        # Check for selector model information
        if hasattr(message, "selector_model"):
            details["selector_model"] = message.selector_model

        # Check for selection context (messages used for selection)
        if hasattr(message, "selection_context"):
            context_len = (
                len(message.selection_context) if message.selection_context else 0
            )
            details["selection_context"] = context_len

        # Check for selector prompt information
        if hasattr(message, "selector_prompt"):
            details["selector_prompt"] = message.selector_prompt

        # Check for selection confidence/score
        if hasattr(message, "selection_confidence"):
            details["selection_confidence"] = message.selection_confidence

        return details if details else None

    @staticmethod
    def detect_selector_event_type(message: Any) -> Optional[str]:
        """Detect specific SelectorGroupChat event types"""

        # Check if this is a speaker selection event
        if hasattr(message, "selector_result") or hasattr(message, "selection_reason"):
            return "speaker_selection"

        # Check if this is a selector model response
        if hasattr(message, "selector_model") and hasattr(message, "selector_prompt"):
            return "selector_model_response"

        # Check if this is a selection failure event
        if hasattr(message, "selection_error") or (
            hasattr(message, "selector_result") and message.selector_result is None
        ):
            return "selection_failure"

        # Check if this is a repeated speaker event
        if hasattr(message, "repeated_speaker_detected"):
            return "repeated_speaker"

        # Check if this is a selector termination event
        if hasattr(message, "selector_termination"):
            return "selector_termination"

        return None

    @staticmethod
    def process_stream_message(message: Any) -> AgentResponse:
        """
        Comprehensive message processing for all AutoGen stream message types.
        Returns a standardized dictionary with all relevant information.
        """
        message_type = MessageProcessor.get_message_type(message)
        source, content, extracted_metadata = MessageProcessor.extract_message_content(
            message
        )
        models_usage = MessageProcessor.extract_models_usage(message)

        # Base message structure
        processed_message = {
            "message_type": message_type.value,
            "source": source,
            "content": content,
            "models_usage": models_usage,
            "metadata": extracted_metadata or {},
        }

        # Add specific details based on message type (merge with existing metadata)
        if message_type == MessageType.TOOL_CALL_REQUEST:
            tool_details = MessageProcessor.extract_tool_call_details(message)
            if tool_details:
                processed_message["metadata"].update(tool_details)

        elif message_type == MessageType.TOOL_CALL_EXECUTION:
            execution_details = MessageProcessor.extract_tool_execution_details(message)
            if execution_details:
                processed_message["metadata"].update(execution_details)

        elif message_type == MessageType.RESPONSE:
            response_details = MessageProcessor.extract_response_details(message)
            if response_details:
                processed_message["metadata"].update(response_details)

        elif message_type == MessageType.TASK_RESULT:
            task_details = MessageProcessor.extract_task_result_details(message)
            if task_details:
                processed_message["metadata"].update(task_details)

        elif message_type == MessageType.HANDOFF_MESSAGE:
            handoff_details = MessageProcessor.extract_handoff_details(message)
            if handoff_details:
                processed_message["metadata"].update(handoff_details)

        # Add group chat details for all message types
        group_chat_details = MessageProcessor.extract_group_chat_details(message)
        if group_chat_details:
            processed_message["metadata"]["group_chat_details"] = group_chat_details

        # Add SelectorGroupChat specific details
        selector_details = MessageProcessor.extract_selector_group_chat_details(message)
        if selector_details:
            processed_message["metadata"]["selector_details"] = selector_details

        # Detect and add selector event type
        selector_event_type = MessageProcessor.detect_selector_event_type(message)
        if selector_event_type:
            processed_message["metadata"]["selector_event_type"] = selector_event_type

        # Add any additional attributes that might be useful
        if hasattr(message, "id"):
            processed_message["metadata"]["message_id"] = message.id

        if hasattr(message, "timestamp"):
            processed_message["timestamp"] = message.timestamp

        # Add message class information for debugging
        processed_message["metadata"]["message_class"] = type(message).__name__

        # Add any additional common attributes
        for attr in ["context", "session_id", "conversation_id", "turn_id"]:
            if hasattr(message, attr):
                processed_message["metadata"][attr] = getattr(message, attr)

        # For streaming chunks, add chunk-specific info
        if message_type == MessageType.STREAMING_CHUNK:
            if hasattr(message, "chunk"):
                processed_message["metadata"]["chunk_info"] = {
                    "chunk_type": type(message.chunk).__name__,
                    "chunk_data": str(message.chunk),
                }

        return AgentResponse(**processed_message)
