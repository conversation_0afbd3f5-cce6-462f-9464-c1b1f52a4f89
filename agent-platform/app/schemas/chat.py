from dataclasses import dataclass
from enum import Enum
from typing import Dict, Optional


class MessageType(Enum):
    """Enum for different message types"""

    TEXT = "text"
    MULTIMODAL = "multimodal"
    TOOL_CALL_REQUEST = "tool_call_request"
    TOOL_CALL_EXECUTION = "tool_call_execution"
    TOOL_CALL_SUMMARY = "tool_call_summary"
    TASK_RESULT = "task_result"
    STREAMING_CHUNK = "streaming_chunk"
    USER_INPUT_REQUEST = "user_input_request"
    RESPONSE = "response"
    AGENT_EVENT = "agent_event"
    # Group Chat and Team specific message types
    HANDOFF_MESSAGE = "handoff_message"
    SPEAKER_SELECTION = "speaker_selection"
    GROUP_CHAT_MESSAGE = "group_chat_message"
    TEAM_MESSAGE = "team_message"
    TERMINATION_MESSAGE = "termination_message"
    UNKNOWN = "unknown"


@dataclass
class AgentResponse:
    """Standardized agent response structure"""

    content: str
    source: str
    models_usage: Optional[Dict[str, int]] = None
    message_type: str = "text"
    metadata: Optional[Dict] = None

    def to_dict(self) -> Dict:
        """Convert to dictionary for API response"""
        return {
            "content": self.content,
            "source": self.source,
            "models_usage": self.models_usage
            or {"prompt_tokens": 0, "completion_tokens": 0},
            "message_type": self.message_type,
            "metadata": self.metadata,
        }
